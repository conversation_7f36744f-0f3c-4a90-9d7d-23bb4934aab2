﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Data;

namespace CPHI.Spark.Loader.Services.GenericStockLoader
{
   public interface IGenericStockJobServiceParams
   {

      //public static readonly GenericStockLoaderParams reportLoadParams;

      //public GenericStockLoaderParams reportLoadParams { get; set; }

      public int RowsToSkip { get; set; }
      public bool IncludePreviousUses { get; set; }
      public bool IncludeAccountStatuses { get; set; }
      public DealerGroupName DealerGroupName { get; set; }
      public string FileExt { get; set; }
      public string DbConnectionName { get; set; }
      public string DbConnectionString { get; set; }
      public string[] AllMatchingFiles { get; set; }
      public string AlternateMergeSP { get; set; }
      public string FinalSPToRun { get; set; }
      public bool TriggerUpdate { get; set; }
      public string JobName { get; set; }
      public string OnlyForSiteIds { get; set; }

#nullable enable
      public string? TabName { get; set; } 
#nullable disable

       void GetMatchingFilesAndImportParams(string incomingRoot);
      Dictionary<string, int> BuildHeaderDictionary(List<string> headers);
      Stock ConvertRowToStock(List<string> rowCells, GenericStockLoaderDbLookups lookups, Dictionary<string, int> headerDictionary);
      DataTable ConvertToDataTable(List<Stock> toReturn);


   }
}
