CREATE OR ALTER PROCEDURE [import].[ME<PERSON><PERSON>_Loader_<PERSON>s_<PERSON><PERSON><PERSON>ill]
    @fileDate      DATETIME,
    @dealerGroupId INT,
    @siteIds       NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -------------------------------------------------------------------------
    -- Backup existing live stocks 
    -------------------------------------------------------------------------
    IF OBJECT_ID('tempdb..#stocksBackup') IS NOT NULL DROP TABLE #stocksBackup;

    DECLARE @InPrepVehicleTypeId INT =
    (
      SELECT TOP (1) Id
      FROM dbo.VehicleTypes
      WHERE Description = 'In Preparation'
      AND <PERSON>erGroup_Id = 36
    );

    SELECT *
    INTO #stocksBackup
    FROM dbo.Stocks
    WHERE IsRemoved = 0;

    -------------------------------------------------------------------------
    -- Merge output capture
    -------------------------------------------------------------------------
    DECLARE @MergeOutput TABLE
    (
        ActionType             VARCHAR(50),
        InsertStockNumberFull  NVARCHAR(50),
        InsertIsRemoved        BIT,
        DeleteStockNumberFull  NVARCHAR(50),
        DeleteIsRemoved        BIT
    );

    -------------------------------------------------------------------------
    -- Parse @siteIds -> table of INTs
    -------------------------------------------------------------------------
    DECLARE @SiteIdTable TABLE (SiteId INT PRIMARY KEY);

    IF @siteIds IS NOT NULL AND LTRIM(RTRIM(@siteIds)) <> ''
    BEGIN
        INSERT INTO @SiteIdTable (SiteId)
        SELECT DISTINCT TRY_CAST(LTRIM(RTRIM(value)) AS INT)
        FROM STRING_SPLIT(@siteIds, ',')
        WHERE LTRIM(RTRIM(value)) <> ''
          AND TRY_CAST(LTRIM(RTRIM(value)) AS INT) IS NOT NULL;
    END

    -------------------------------------------------------------------------
    -- MERGE dbo.Stocks (scope by DealerGroup and optional site list)
    -------------------------------------------------------------------------
    MERGE dbo.Stocks AS TARGET
    USING
    (
        SELECT
            S.[StockNumber],
            S.[Reg],
            S.[IsVatQ],
            S.[Make],
            S.[Model],
            S.[Description],
            S.[Chassis],
            S.[Colour],
            S.[Mileage],
            S.[StockDate],
            S.[RegDate],
            S.[Dis],
            S.[Purchased],
            S.[Siv],
            S.[Fuel],
            S.[Selling],
            S.[Site_Id],
            S.[Doors],
            S.[Transmission],
            S.[DaysAtBranch],
            S.[NonRecoverableCosts],
            S.[DealerFitAccessories],
            S.[OptionCosts],
            S.[PreviousSite_Id],
            S.[DisposalRoute_Id],
            S.[SupplierOrderNo],
            S.[C02],
            S.[CapValue],
            S.[CapProvision],
            S.[CapID],
            S.[CapNotes],
            S.[PartExStockBook],
            S.[PhysicalLocation],
            S.[CapCode],
            S.[VariantClass],
            S.[Options],
            S.[AccountStatus_Id],
            S.[ProgressCode_Id],
            S.[VehicleType_Id],
            S.[StockNumberSuffix],
            S.[StockNumberFull],
            S.[ReservedDate],
            S.[PreviousUse_Id],
            S.[LastUpdatedDate],
            S.[PreviousReg],
            S.[IsRemoved],
            S.[IsUpdated],
            S.[RemovedDate],
            S.[ModelYear],
            S.[BranchStockDate],
            S.[UniqueId],
            S.[ImageCount],
            S.[IdFamily],
            S.[IdBrand],
            S.[IdLocation],
            S.[IdDataSource],
            S.[VehicleAdvert_Id],
            S.[Ubicacion],
            S.[DateFactoryTransportation],
            S.[DateSiteArrival],
            S.[DateSiteTransportation],
            S.[DateVehicleRecondition],
            S.[OriginalPurchasePrice],
            S.[Buyer],
            S.[BoughtFrom],
            S.[BoughtFromGroup],
            S.[DisplayDate],
            S.[IsSOR]
        FROM import.Stocks AS S   -- you said this is pre-filtered to the DG
    ) AS SOURCE
      ON  TARGET.StockNumberFull = SOURCE.StockNumberFull
      AND EXISTS
      (
          SELECT 1
          FROM dbo.Sites si
          WHERE si.Id = TARGET.Site_Id
            AND si.DealerGroup_Id = @dealerGroupId
      )
      AND (
            NOT EXISTS (SELECT 1 FROM @SiteIdTable)
         OR TARGET.Site_Id IN (SELECT SiteId FROM @SiteIdTable)
      )

    -- INSERT new rows within scope
    WHEN NOT MATCHED BY TARGET
      AND EXISTS
      (
          SELECT 1
          FROM dbo.Sites si
          WHERE si.Id = SOURCE.Site_Id
            AND si.DealerGroup_Id = @dealerGroupId
      )
      AND (
            NOT EXISTS (SELECT 1 FROM @SiteIdTable)
         OR SOURCE.Site_Id IN (SELECT SiteId FROM @SiteIdTable)
      )
    THEN
      INSERT
      (
        [StockNumber],
        [Reg],
        [IsVatQ],
        [Make],
        [Model],
        [Description],
        [Chassis],
        [Colour],
        [Mileage],
        [StockDate],
        [RegDate],
        [Dis],
        [Purchased],
        [Siv],
        [Fuel],
        [Selling],
        [Site_Id],
        [Doors],
        [Transmission],
        [DaysAtBranch],
        [NonRecoverableCosts],
        [DealerFitAccessories],
        [OptionCosts],
        [PreviousSite_Id],
        [DisposalRoute_Id],
        [SupplierOrderNo],
        [C02],
        [CapValue],
        [CapProvision],
        [CapID],
        [CapNotes],
        [PartExStockBook],
        [PhysicalLocation],
        [CapCode],
        [VariantClass],
        [Options],
        [AccountStatus_Id],
        [ProgressCode_Id],
        [VehicleType_Id],
        [StockNumberSuffix],
        [StockNumberFull],
        [ReservedDate],
        [PreviousUse_Id],
        [LastUpdatedDate],
        [PreviousReg],
        [IsRemoved],
        [IsUpdated],
        [RemovedDate],
        [ModelYear],
        [BranchStockDate],
        [UniqueId],
        [ImageCount],
        [IdFamily],
        [IdBrand],
        [IdLocation],
        [IdDataSource],
        [VehicleAdvert_Id],
        [Ubicacion],
        [DateFactoryTransportation],
        [DateSiteArrival],
        [DateSiteTransportation],
        [DateVehicleRecondition],
        [OriginalPurchasePrice],
        [Buyer],
        [BoughtFrom],
        [BoughtFromGroup],
        [DisplayDate],
        [IsSOR]
      )
      VALUES
      (
        SOURCE.[StockNumber],
        SOURCE.[Reg],
        SOURCE.[IsVatQ],
        SOURCE.[Make],
        SOURCE.[Model],
        SOURCE.[Description],
        SOURCE.[Chassis],
        SOURCE.[Colour],
        SOURCE.[Mileage],
        SOURCE.[StockDate],
        SOURCE.[RegDate],
        SOURCE.[Dis],
        SOURCE.[Purchased],
        SOURCE.[Siv],
        SOURCE.[Fuel],
        SOURCE.[Selling],
        SOURCE.[Site_Id],
        SOURCE.[Doors],
        SOURCE.[Transmission],
        SOURCE.[DaysAtBranch],
        SOURCE.[NonRecoverableCosts],
        SOURCE.[DealerFitAccessories],
        SOURCE.[OptionCosts],
        SOURCE.[PreviousSite_Id],
        SOURCE.[DisposalRoute_Id],
        SOURCE.[SupplierOrderNo],
        SOURCE.[C02],
        SOURCE.[CapValue],
        SOURCE.[CapProvision],
        SOURCE.[CapID],
        SOURCE.[CapNotes],
        SOURCE.[PartExStockBook],
        SOURCE.[PhysicalLocation],
        SOURCE.[CapCode],
        SOURCE.[VariantClass],
        SOURCE.[Options],
        SOURCE.[AccountStatus_Id],
        SOURCE.[ProgressCode_Id],
        SOURCE.[VehicleType_Id],
        SOURCE.[StockNumberSuffix],
        SOURCE.[StockNumberFull],
        SOURCE.[ReservedDate],
        SOURCE.[PreviousUse_Id],
        SOURCE.[LastUpdatedDate],
        SOURCE.[PreviousReg],
        SOURCE.[IsRemoved],
        SOURCE.[IsUpdated],
        SOURCE.[RemovedDate],
        SOURCE.[ModelYear],
        SOURCE.[BranchStockDate],
        SOURCE.[UniqueId],
        SOURCE.[ImageCount],
        SOURCE.[IdFamily],
        SOURCE.[IdBrand],
        SOURCE.[IdLocation],
        SOURCE.[IdDataSource],
        SOURCE.[VehicleAdvert_Id],
        SOURCE.[Ubicacion],
        SOURCE.[DateFactoryTransportation],
        CASE 
            WHEN Source.VehicleType_Id <> @InPrepVehicleTypeId 
                THEN @fileDate       -- new + already out of prep → arrival = today
            ELSE Source.[DateSiteArrival] -- still in prep → null
        END,
        SOURCE.[DateSiteTransportation],
        SOURCE.[DateVehicleRecondition],
        SOURCE.[OriginalPurchasePrice],
        SOURCE.[Buyer],
        SOURCE.[BoughtFrom],
        SOURCE.[BoughtFromGroup],
        SOURCE.[DisplayDate],
        SOURCE.[IsSOR]
      )

    -- UPDATE matches
    WHEN MATCHED THEN
      UPDATE
      SET
        Target.Reg                 = Source.Reg,
        Target.IsVatQ              = Source.IsVatQ,
        Target.Make                = Source.Make,
        Target.Model               = Source.Model,
        Target.Description         = Source.Description,
        Target.Chassis             = Source.Chassis,
        Target.Colour              = Source.Colour,
        Target.Mileage             = Source.Mileage,
        Target.StockDate           = Source.StockDate,
        Target.BranchStockDate     = Source.BranchStockDate,
        Target.RegDate             = Source.RegDate,    
        Target.Purchased           = Source.Purchased,
        Target.Siv                 = Source.Siv,
        Target.Fuel                = Source.Fuel,
        Target.Selling             = Source.Selling,
        Target.Site_Id             = Source.Site_Id,
        Target.Doors               = Source.Doors,
        Target.Transmission        = Source.Transmission,
        Target.DaysAtBranch        = Source.DaysAtBranch,
        Target.NonRecoverableCosts = Source.NonRecoverableCosts,
        Target.DealerFitAccessories= Source.DealerFitAccessories,
        Target.OptionCosts         = Source.OptionCosts,
        Target.PreviousSite_Id     = Source.PreviousSite_Id,
        Target.DisposalRoute_Id    = Source.DisposalRoute_Id,
        Target.SupplierOrderNo     = Source.SupplierOrderNo,
        Target.C02                 = Source.C02,
        Target.CapValue            = Source.CapValue,
        Target.CapProvision        = Source.CapProvision,
        Target.CapID               = Source.CapID,
        Target.CapNotes            = Source.CapNotes,
        Target.PartExStockBook     = Source.PartExStockBook,
        Target.PhysicalLocation    = Source.PhysicalLocation,
        Target.CapCode             = Source.CapCode,
        Target.VariantClass        = Source.VariantClass,
        Target.Options             = Source.Options,
        Target.AccountStatus_Id    = Source.AccountStatus_Id,
        Target.ProgressCode_Id     = Source.ProgressCode_Id,
        Target.VehicleType_Id      = Source.VehicleType_Id,
        Target.StockNumberSuffix   = Source.StockNumberSuffix,
        Target.ReservedDate        = Source.ReservedDate,
        Target.PreviousUse_Id      = Source.PreviousUse_Id,
        Target.PreviousReg         = Source.PreviousReg,
        Target.ModelYear           = Source.ModelYear,
        Target.VehicleAdvert_Id    = Source.VehicleAdvert_Id,
        Target.IsUpdated           = 1,
        Target.LastUpdatedDate     = GETUTCDATE(),
        Target.[UniqueId]          = Source.[UniqueId],
        Target.[ImageCount]        = Source.[ImageCount],
        Target.[IdFamily]          = Source.[IdFamily],
        Target.[IdBrand]           = Source.[IdBrand],
        Target.[IdLocation]        = Source.[IdLocation],
        Target.[IdDataSource]      = Source.[IdDataSource],
        Target.[Ubicacion]         = Source.[Ubicacion],
        Target.[DateFactoryTransportation] = Source.[DateFactoryTransportation],

        -- Gravel Hill specific changes
        -- If VehicleType changes FROM 'In Preparation', set DateSiteArrival to file date
        Target.[DateSiteArrival] =
            CASE
                -- 1. If it was "In Preparation" before (Target) and has just left in Source,
                --    set arrival to @fileDate (today’s file date).
                WHEN Target.VehicleType_Id = @InPrepVehicleTypeId
                 AND Source.VehicleType_Id <> @InPrepVehicleTypeId
                THEN @fileDate

                -- 2. If both Target and Source are already NOT "In Preparation",
                --    do not overwrite; keep the existing Target.DateSiteArrival.
                WHEN Target.VehicleType_Id <> @InPrepVehicleTypeId
                 AND Source.VehicleType_Id <> @InPrepVehicleTypeId
                THEN Target.[DateSiteArrival]

                -- 3. Otherwise (still in prep, or no change),
                --   then null
                ELSE NULL
            END,

        -- Dis: days since DateSiteArrival, or 0 if the latest VehicleType is still "In Preparation"
        Target.[Dis] =
            CASE
                -- 1. If it's still in prep, set to 0
                WHEN Source.VehicleType_Id = @InPrepVehicleTypeId
                    THEN 0

                -- If moving out of InPrep on this file (Target was InPrep, Source not) => 0 days today
                -- May need to change this
                WHEN Target.VehicleType_Id = @InPrepVehicleTypeId
                AND Source.VehicleType_Id <> @InPrepVehicleTypeId
                  THEN 0  -- DATEDIFF(DAY, @fileDate, @fileDate) = 0

                -- Otherwise compute from the main table's DateSiteArrival
                -- Has already moved to Sale, calculate new DIS
                WHEN Target.[DateSiteArrival] IS NOT NULL
                  THEN DATEDIFF(DAY, Target.[DateSiteArrival], @fileDate)

                ELSE Target.[Dis]
            END,

        Target.[DateSiteTransportation]   = Source.[DateSiteTransportation],
        Target.[DateVehicleRecondition]   = Source.[DateVehicleRecondition],
        Target.[OriginalPurchasePrice]    = Source.[OriginalPurchasePrice],
        Target.[Buyer]                    = Source.[Buyer],
        Target.[BoughtFrom]               = Source.[BoughtFrom],
        Target.[BoughtFromGroup]          = Source.[BoughtFromGroup],
        Target.[DisplayDate]              = Source.[DisplayDate],
        Target.[IsSOR]                    = Source.[IsSOR]

    -- Soft-delete rows that disappeared from SOURCE within the same scope
    WHEN NOT MATCHED BY SOURCE
      AND TARGET.IsRemoved = 0
    THEN
      UPDATE
      SET
        Target.IsRemoved       = 1,
        Target.RemovedDate     = @fileDate,
        Target.LastUpdatedDate = @fileDate

    OUTPUT
        $action                           AS ActionType,
        inserted.StockNumberFull          AS InsertStockNumberFull,
        inserted.IsRemoved                AS InsertIsRemoved,
        deleted.StockNumberFull           AS DeleteStockNumberFull,
        deleted.IsRemoved                 AS DeleteIsRemoved
    INTO @MergeOutput;

END
GO
